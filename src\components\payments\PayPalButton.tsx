import React from 'react';
import { PayPalButtons, PayPalScriptProvider } from '@paypal/react-paypal-js';
import { usePixel } from '@/shared/hooks';

// Tipos para PayPal
interface PayPalOrderDetails {
  id: string;
  status: string;
  purchase_units: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    payments: {
      captures: Array<{
        id: string;
        status: string;
        amount: {
          currency_code: string;
          value: string;
        };
      }>;
    };
  }>;
  payer: {
    name: {
      given_name: string;
      surname: string;
    };
    email_address: string;
  };
}

interface PayPalError {
  message: string;
  details?: Array<{
    issue: string;
    description: string;
  }>;
}

interface PayPalButtonProps {
  amount: string;
  currency?: string;
  description?: string;
  onSuccess?: (details: PayPalOrderDetails) => void;
  onError?: (error: PayPalError) => void;
  onCancel?: () => void;
  disabled?: boolean;
  style?: {
    layout?: 'vertical' | 'horizontal';
    color?: 'gold' | 'blue' | 'silver' | 'white' | 'black';
    shape?: 'rect' | 'pill';
    label?: 'paypal' | 'checkout' | 'buynow' | 'pay' | 'installment';
    tagline?: boolean;
    height?: number;
  };
}

const PayPalButton: React.FC<PayPalButtonProps> = ({
  amount,
  currency = 'USD',
  description = 'BajaTravel Transportation Service',
  onSuccess,
  onError,
  onCancel,
  disabled = false,
  style = {
    layout: 'vertical',
    color: 'gold',
    shape: 'rect',
    label: 'pay',
    tagline: false,
    height: 50
  }
}) => {
  const pixel = usePixel();

  // Configuración inicial de PayPal
  const initialOptions = {
    clientId: import.meta.env.VITE_PAYPAL_CLIENT_ID || 'test',
    currency: currency,
    intent: 'capture',
    components: 'buttons',
    'enable-funding': 'venmo,paylater',
    'disable-funding': 'credit,card',
    // ✅ Agregar configuración de entorno para sandbox
    'data-sdk-integration-source': 'developer-studio',
    environment: import.meta.env.VITE_PAYPAL_ENVIRONMENT || 'sandbox'
  };

  // Crear orden de PayPal
  const createOrder = (data: unknown, actions: any) => {
    console.log('Creating PayPal order...', { amount, currency, description });

    return actions.order.create({
      purchase_units: [
        {
          amount: {
            currency_code: currency,
            value: amount,
          },
          description: description,
        },
      ],
      application_context: {
        shipping_preference: 'NO_SHIPPING', // No necesitamos dirección de envío para servicios
      },
    });
  };

  // Aprobar pago
  const onApprove = async (data: unknown, actions: any) => {
    try {
      console.log('PayPal payment approved:', data);

      const details: PayPalOrderDetails = await actions.order.capture();
      console.log('PayPal payment captured:', details);

      // Tracking de Facebook Pixel
      pixel.addPaymentInfo({
        value: parseFloat(amount),
        currency: currency,
        content_type: 'product',
        content_ids: ['transportation_service'],
      });

      pixel.purchase({
        value: parseFloat(amount),
        currency: currency,
        content_type: 'product',
        content_ids: ['transportation_service'],
      });

      // Callback de éxito
      if (onSuccess) {
        onSuccess(details);
      }

      return details;
    } catch (error) {
      console.error('Error capturing PayPal payment:', error);
      if (onError && error instanceof Error) {
        onError({ message: error.message });
      }
    }
  };

  // Manejar errores
  const onErrorHandler = (error: PayPalError) => {
    console.error('PayPal payment error:', error);
    if (onError) {
      onError(error);
    }
  };

  // Manejar cancelación
  const onCancelHandler = () => {
    console.log('PayPal payment cancelled');
    if (onCancel) {
      onCancel();
    }
  };

  if (disabled) {
    return (
      <div className="opacity-50 cursor-not-allowed">
        <div className="bg-gray-200 rounded-lg p-4 text-center text-gray-500">
          PayPal payment disabled
        </div>
      </div>
    );
  }

  return (
    <div className="paypal-button-container">
      <PayPalScriptProvider options={initialOptions}>
        <PayPalButtons
          style={style}
          createOrder={createOrder}
          onApprove={onApprove}
          onError={onErrorHandler}
          onCancel={onCancelHandler}
          disabled={disabled}
        />
      </PayPalScriptProvider>
    </div>
  );
};

export default PayPalButton;
