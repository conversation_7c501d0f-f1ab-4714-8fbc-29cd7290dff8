import { useLocation, useNavigate } from 'react-router-dom';
import TopBar from '@/components/TopBar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

// Components
import {
  BackNavigation,
  BookingDetailsHeader,
  TransferBookingCard,
  ContactInformationCard,
  FlightInformationCard,
  AdditionalItemsCard,
  CheckoutButton
} from './components';

// Hooks
import { useBookingDetailsForm } from './hooks/useBookingDetailsForm';
import { useBooking } from '@/context/BookingContext';
import { useSaveBooking } from '@/features/retails';

// Utils
import { calculateTotalTransportationPrice } from '@/utils/additionalItemsCalculations';
import { transformBookingToRetail, validateBookingData } from '@/utils/bookingPersistence';

const BookingDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const serviceData = location.state;
  const { state } = useBooking();
  const saveBookingMutation = useSaveBooking();

  // Form management with React Hook Form
  const {
    control,
    handleSubmit,
    errors,
    isValid,
    onSubmit,
    isBookingComplete
  } = useBookingDetailsForm();

  // Calculate total price including additional items
  const calculateTotalPrice = () => {
    if (!serviceData?.price) return { totalPrice: 0, basePrice: 0, additionalItemsCost: 0 };

    return calculateTotalTransportationPrice(
      serviceData.price,
      state.additionalItems
    );
  };

  const priceCalculation = calculateTotalPrice();

  // Navigation handlers
  const handleBackNavigation = () => {
    navigate('/booking-service');
  };

  const handleModifyBooking = () => {
    navigate('/booking-service');
  };

  // Form submission for traditional checkout
  const handleProceedToCheckout = handleSubmit((data) => {
    const completeBookingData = onSubmit(data);

    console.log('Proceeding to checkout with complete data:', completeBookingData);

    // Aquí puedes navegar al checkout o enviar datos a la API
    // navigate('/checkout', { state: completeBookingData });
  });

  // PayPal payment handlers
  const handlePayPalSuccess = async (details: any) => {
    console.log('PayPal payment successful:', details);

    try {
      // Complete the booking form first
      const formData = await new Promise<any>((resolve) => {
        handleSubmit((data) => {
          const completeBookingData = onSubmit(data);

          // Add PayPal payment information
          const bookingWithPayment = {
            ...completeBookingData,
            payment: {
              method: 'paypal',
              transactionId: details.id,
              status: details.status,
              amount: priceCalculation.totalPrice,
              currency: 'USD',
              payerInfo: details.payer,
              captureId: details.purchase_units?.[0]?.payments?.captures?.[0]?.id || null,
              payerEmail: details.payer?.email_address || null,
              payerName: `${details.payer?.name?.given_name || ''} ${details.payer?.name?.surname || ''}`.trim()
            },
            serviceData: serviceData,
            totalPrice: priceCalculation.totalPrice,
            createdAt: new Date().toISOString()
          };

          resolve(bookingWithPayment);
        })();
      });

      console.log('Booking data prepared:', formData);

      // Validate booking data
      const validationErrors = validateBookingData(formData, state);
      if (validationErrors.length > 0) {
        console.error('Validation errors:', validationErrors);
        alert(`Validation errors: ${validationErrors.join(', ')}`);
        return;
      }

      // Transform booking data to retail format
      const retailData = transformBookingToRetail(formData, state, details);
      console.log('Retail data prepared:', retailData);

      // Save booking using the retails service
      const result = await saveBookingMutation.mutateAsync(retailData);
      console.log('Booking saved successfully:', result);

      // Navigate to confirmation page
      navigate('/booking-confirmation', {
        state: {
          bookingId: result.transaction_id,
          paymentDetails: details,
          bookingData: formData,
          retailData: retailData
        }
      });

    } catch (error) {
      console.error('Error processing PayPal success:', error);

      // Show error but don't lose the payment info
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Payment successful but booking save failed: ${errorMessage}. Payment ID: ${details.id}. Please contact support.`);
    }
  };

  const handlePayPalError = (error: any) => {
    console.error('PayPal payment failed:', error);
    // Handle payment error (show notification, etc.)
  };

  // Cash payment handler
  const handleCashPayment = async () => {
    console.log('Cash payment selected');

    try {
      // Complete the booking form first
      const formData = await new Promise<any>((resolve) => {
        handleSubmit((data) => {
          const completeBookingData = onSubmit(data);

          // Add cash payment information
          const bookingWithPayment = {
            ...completeBookingData,
            payment: {
              method: 'cash',
              transactionId: `CASH_${Date.now()}`,
              status: 'pending',
              amount: priceCalculation.totalPrice,
              currency: 'USD',
              payerInfo: null,
              captureId: null,
              payerEmail: completeBookingData.contactInfo?.email || null,
              payerName: completeBookingData.contactInfo?.name || null
            },
            serviceData: serviceData,
            totalPrice: priceCalculation.totalPrice,
            createdAt: new Date().toISOString()
          };

          resolve(bookingWithPayment);
        })();
      });

      console.log('Cash booking data prepared:', formData);

      // Validate booking data
      const validationErrors = validateBookingData(formData, state);
      if (validationErrors.length > 0) {
        console.error('Validation errors:', validationErrors);
        alert(`Validation errors: ${validationErrors.join(', ')}`);
        return;
      }

      // Transform booking data to retail format
      const retailData = transformBookingToRetail(formData, state, { id: formData.payment.transactionId });

      // Update payment method to cash
      retailData.payment_method = 'Cash';
      retailData.authorization_code = formData.payment.transactionId;

      console.log('Cash retail data prepared:', retailData);

      // Save booking using the retails service
      const result = await saveBookingMutation.mutateAsync(retailData);
      console.log('Cash booking saved successfully:', result);

      // Navigate to confirmation page
      navigate('/booking-confirmation', {
        state: {
          bookingId: result.transaction_id,
          paymentDetails: {
            id: formData.payment.transactionId,
            status: 'pending',
            payment_method: 'cash'
          },
          bookingData: formData,
          retailData: retailData
        }
      });

    } catch (error) {
      console.error('Error processing cash payment:', error);

      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Failed to process cash payment: ${errorMessage}. Please try again.`);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <TopBar />
      <Header />
      
      <div className="container mx-auto px-4 py-8 md:mt-16">
        {/* Back Navigation */}
        <BackNavigation onBack={handleBackNavigation} />

        {/* Page Header */}
        <BookingDetailsHeader />

        <form onSubmit={handleProceedToCheckout} className="space-y-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Left Column - Transfer Booking Details */}
            <div className="lg:col-span-1 space-y-6">
              <TransferBookingCard
                serviceData={serviceData}
                onModifyBooking={handleModifyBooking}
              />


            </div>

            {/* Right Column - Forms */}
            <div className="lg:col-span-2 space-y-6">
              {/* Contact Information */}
              <ContactInformationCard
                control={control}
                errors={errors}
              />

              {/* Flight Information */}
              <FlightInformationCard
                control={control}
                errors={errors}
              />

              {/* Additional Items */}
              <AdditionalItemsCard
                control={control}
                errors={errors}
              />

              {/* Checkout Button */}
              <CheckoutButton
                onSubmit={handleProceedToCheckout}
                disabled={!isValid}
                text={isBookingComplete ? "Booking Complete!" : "Proceed to checkout"}
                amount={priceCalculation.totalPrice.toString()}
                currency="USD"
                description={`${serviceData?.name || 'Transportation Service'} - BajaTravel`}
                enablePayPal={true}
                onPayPalSuccess={handlePayPalSuccess}
                onPayPalError={handlePayPalError}
                onCashPayment={handleCashPayment}
              />
            </div>
          </div>
        </form>
      </div>

      <Footer />
    </div>
  );
};

export default BookingDetails;
