import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlaneIcon, UsersIcon } from 'lucide-react';
import { useBooking } from '@/context/BookingContext';
import { format } from 'date-fns';
import RoundTripSavingsBanner from './RoundTripSavingsBanner';
import {
  calculateTotalTransportationPrice,
  formatPrice,
  generateAdditionalItemsSummary
} from '@/utils/additionalItemsCalculations';
import { useState } from 'react';

interface ServiceData {
  id: number;
  name: string;
  type: string;
  price: number;
  image?: string;
}

interface TransferBookingCardProps {
  serviceData?: ServiceData;
  onModifyBooking: () => void;
}

const TransferBookingCard = ({ serviceData, onModifyBooking }: TransferBookingCardProps) => {
  const { state } = useBooking();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Calcular precio total incluyendo servicios adicionales
  const calculateTotalPrice = () => {
    if (!serviceData) return null;

    return calculateTotalTransportationPrice(
      serviceData.price,
      state.additionalItems
    );
  };

  const priceCalculation = calculateTotalPrice();
  const additionalItemsSummary = generateAdditionalItemsSummary(state.additionalItems);

  // Formatear fecha para mostrar
  const formatDate = (date?: Date) => {
    if (!date) return 'Not selected';
    return format(date, 'MMM dd, yyyy');
  };

  // Obtener nombre del aeropuerto de origen
  const getFromAirportDisplay = () => {
    if (state.fromAirportInfo) {
      return `${state.fromAirportInfo.name} (${state.fromAirportInfo.code})`;
    }
    return state.from || 'Not selected';
  };

  // Obtener nombre del destino
  const getToDisplay = () => {
    if (state.selectedHotel) {
      return state.selectedHotel.name;
    }
    return state.to || 'Not selected';
  };

  // Calcular total de pasajeros
  const getTotalPassengers = () => {
    const adults = parseInt(state.adults) || 0;
    const kids = parseInt(state.kids) || 0;
    return adults + kids;
  };

  return (
    <Card className="bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-white border-b pb-4">
        <CardTitle className="flex items-center gap-2 text-primary text-lg">
          <PlaneIcon className="w-5 h-5" />
          Transfer Booking Details
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Transfer Details Section */}
        <div className="space-y-6">
          {/* Arriving Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-primary">
              <PlaneIcon className="w-4 h-4 rotate-45" />
              <span className="font-semibold text-sm">Arriving</span>
            </div>
            <div className="space-y-2 pl-6">
              <div className="text-sm">
                <span className="font-medium">From:</span>
                <span className="ml-2 text-muted-foreground">{getFromAirportDisplay()}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">To:</span>
                <span className="ml-2 text-muted-foreground">{getToDisplay()}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Date:</span>
                <span className="ml-2 text-muted-foreground">{formatDate(state.date)}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Time:</span>
                {state.time ? (
                  <span className="ml-2 text-muted-foreground">{state.time}</span>
                ) : (
                  <span className="ml-2 text-xs text-amber-600 italic">Select time in Flight Information</span>
                )}
              </div>
            </div>
          </div>

          {/* Departing Section - Only show if roundTrip is true */}
          {state.roundTrip && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-primary">
                <PlaneIcon className="w-4 h-4 -rotate-45" />
                <span className="font-semibold text-sm">Departing</span>
              </div>
              <div className="space-y-2 pl-6">
                <div className="text-sm">
                  <span className="font-medium">From:</span>
                  <span className="ml-2 text-muted-foreground">{getToDisplay()}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">To:</span>
                  <span className="ml-2 text-muted-foreground">{getFromAirportDisplay()}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Date:</span>
                  <span className="ml-2 text-muted-foreground">{formatDate(state.returnDate)}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Time:</span>
                  {state.returnTime ? (
                    <span className="ml-2 text-muted-foreground">{state.returnTime}</span>
                  ) : (
                    <span className="ml-2 text-xs text-amber-600 italic">Select time in Flight Information</span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Passengers Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-primary">
            <UsersIcon className="w-4 h-4" />
            <span className="font-semibold text-sm">Passengers</span>
          </div>
          <div className="pl-6 space-y-1">
            <div className="text-sm">
              <span className="font-medium">Adults:</span>
              <span className="ml-2 text-muted-foreground">{state.adults}</span>
            </div>
            {parseInt(state.kids) > 0 && (
              <div className="text-sm">
                <span className="font-medium">Kids:</span>
                <span className="ml-2 text-muted-foreground">{state.kids}</span>
              </div>
            )}
            <div className="text-sm font-medium">
              <span className="font-medium">Total:</span>
              <span className="ml-2 text-primary">{getTotalPassengers()} passengers</span>
            </div>
          </div>
        </div>

        {/* Service Details Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-primary">
            <span className="text-yellow-500">🚗</span>
            <span className="font-semibold text-sm">Service Details</span>
          </div>
          <div className="pl-6">
            <div className="flex items-start gap-3">
              {/* Vehicle Image Thumbnail */}
              <div className="w-16 h-12 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                {serviceData?.image ? (
                  <>
                    {/* Skeleton loader while image loads */}
                    {!imageLoaded && (
                      <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
                        <span className="text-xs text-gray-400">🚙</span>
                      </div>
                    )}
                    {/* Vehicle Image */}
                    <img
                      src={serviceData.image}
                      alt={serviceData.name}
                      className={`w-full h-full object-cover transition-opacity duration-300 ${
                        !imageLoaded ? 'opacity-0 absolute' : 'opacity-100'
                      }`}
                      onLoad={() => setImageLoaded(true)}
                      onError={() => {
                        setImageError(true);
                        setImageLoaded(false);
                      }}
                      loading="lazy"
                    />
                    {/* Fallback if image fails to load */}
                    {imageError && (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <span className="text-xs text-gray-400">🚙</span>
                      </div>
                    )}
                  </>
                ) : (
                  <span className="text-xs">🚙</span>
                )}
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">
                  {state.roundTrip ? 'Round Trip' : 'One Way'}
                </div>
                <div className="text-xs text-muted-foreground">Private Service</div>
                <div className="text-xs text-muted-foreground">
                  {serviceData?.type || 'SUV Transportation'}
                </div>
                {state.selectedHotel && (
                  <div className="text-xs text-muted-foreground mt-1">
                    Zone: {state.selectedHotel.zone_name || `Zone ${state.selectedHotel.zone_id}`}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Modify Booking Button */}
        <div className="pt-4">
          <Button 
            variant="outline"
            className="w-full border-primary text-primary hover:bg-primary hover:text-white transition-colors"
            onClick={onModifyBooking}
          >
            <span className="mr-2">✏️</span>
            Modify Booking
          </Button>
        </div>

        {/* Service Summary */}
        {serviceData && priceCalculation && (
          <div className="p-4 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg border border-primary/20">
            <div className="space-y-3">
              <div>
                <h3 className="font-semibold text-lg">{serviceData.name}</h3>
                <p className="text-sm text-muted-foreground">{serviceData.type}</p>
              </div>

              {/* Price Breakdown */}
              <div className="space-y-2">
                {/* Base Transportation Price */}
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Transportation</span>
                  <span className="font-medium">{formatPrice(priceCalculation.basePrice)}</span>
                </div>

                {/* Additional Services */}
                {additionalItemsSummary.length > 0 && (
                  <>
                    {additionalItemsSummary.map((item, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">{item.split(':')[0]}</span>
                        <span className="font-medium">{item.split(':')[1]?.trim()}</span>
                      </div>
                    ))}
                  </>
                )}

                {/* Total Price */}
                <div className="flex justify-between items-center pt-2 border-t border-primary/20">
                  <span className="text-sm font-medium">Total Price</span>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{formatPrice(priceCalculation.totalPrice)}</div>
                    <div className="text-xs text-muted-foreground">USD</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Round Trip Savings Banner */}
        <RoundTripSavingsBanner serviceData={serviceData} />
        
        {/* Free Cancellation Banner */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-blue-900">Free Cancellation</span>
          </div>
          <p className="text-xs text-blue-700">Cancel up to 24 hours before your trip</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransferBookingCard;
