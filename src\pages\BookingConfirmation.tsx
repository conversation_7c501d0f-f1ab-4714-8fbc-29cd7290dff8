import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Download, Mail, Phone, Calendar, CreditCard } from 'lucide-react';
import TopBar from '@/components/TopBar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAnalytics } from '@/shared/hooks';

const BookingConfirmation: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { trackEvent } = useAnalytics();
  const { bookingId, paymentDetails, bookingData, retailData } = location.state || {};

  useEffect(() => {
    // Track successful booking
    trackEvent({
      action: 'booking_completed',
      category: 'Booking',
      label: 'PayPal Payment Success',
      value: bookingData?.totalPrice
    });
  }, [trackEvent, bookingData]);

  // Redirect if no booking data
  if (!bookingId || !paymentDetails || !bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 mt-16">
        <TopBar />
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-8">
              <p className="text-gray-600 mb-4">No booking information found.</p>
              <Button onClick={() => navigate('/')}>
                Return to Home
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Not specified';
    return timeString;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopBar />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Success Header */}
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-green-800 mb-2">
                Booking Confirmed!
              </h1>
              <p className="text-green-700 text-lg mb-4">
                Your transportation has been successfully booked and paid for.
              </p>
              <div className="flex justify-center items-center space-x-4 flex-wrap gap-2">
                <Badge variant="outline" className="bg-white text-green-800 border-green-300">
                  Transaction ID: {bookingId}
                </Badge>
                <Badge variant="outline" className="bg-white text-blue-800 border-blue-300">
                  PayPal ID: {paymentDetails.id}
                </Badge>
                {retailData && (
                  <Badge variant="outline" className="bg-white text-purple-800 border-purple-300">
                    Fleet: {retailData.fleet.name}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Booking Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Booking Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Service</h3>
                  <p className="text-gray-600">{bookingData.serviceData?.name || 'Transportation Service'}</p>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900">Contact Information</h3>
                  <p className="text-gray-600">{bookingData.contactInfo?.name}</p>
                  <p className="text-gray-600">{bookingData.contactInfo?.email}</p>
                  <p className="text-gray-600">{bookingData.contactInfo?.phone}</p>
                </div>

                {bookingData.flightInfo?.flightNumber && (
                  <div>
                    <h3 className="font-semibold text-gray-900">Flight Information</h3>
                    <p className="text-gray-600">
                      Flight: {bookingData.flightInfo.airline} {bookingData.flightInfo.flightNumber}
                    </p>
                    <p className="text-gray-600">
                      Arrival: {formatDate(bookingData.flightInfo.arrivalDate)} at {formatTime(bookingData.flightInfo.arrivalTime)}
                    </p>
                    {bookingData.flightInfo.departureDate && (
                      <p className="text-gray-600">
                        Departure: {formatDate(bookingData.flightInfo.departureDate)} at {formatTime(bookingData.flightInfo.departureTime)}
                      </p>
                    )}
                  </div>
                )}

                {bookingData.additionalItems?.specialInstructions && (
                  <div>
                    <h3 className="font-semibold text-gray-900">Special Instructions</h3>
                    <p className="text-gray-600">{bookingData.additionalItems.specialInstructions}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-semibold">Total Amount:</span>
                  <span className="text-xl font-bold text-green-600">
                    ${bookingData.totalPrice} USD
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Payment Method:</span>
                  <Badge className="bg-blue-100 text-blue-800">PayPal</Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Transaction ID:</span>
                  <span className="font-mono text-sm">{paymentDetails.id}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Payment Status:</span>
                  <Badge className="bg-green-100 text-green-800">Completed</Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Payer:</span>
                  <span>{paymentDetails.payer?.email_address}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span>Payment Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <Card className="mt-8">
            <CardContent className="py-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={() => window.print()}
                  variant="outline"
                  className="flex items-center"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Print Confirmation
                </Button>
                
                <Button 
                  onClick={() => window.location.href = `mailto:${bookingData.contactInfo?.email}?subject=Booking Confirmation - ${bookingId}`}
                  variant="outline"
                  className="flex items-center"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Email Confirmation
                </Button>
                
                <Button 
                  onClick={() => window.location.href = 'tel:+526241558009'}
                  variant="outline"
                  className="flex items-center"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
                
                <Button 
                  onClick={() => navigate('/')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Book Another Trip
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Important Information */}
          <Card className="mt-8 border-yellow-200 bg-yellow-50">
            <CardContent className="py-6">
              <h3 className="font-semibold text-yellow-800 mb-3">Important Information</h3>
              <ul className="text-yellow-700 space-y-2 text-sm">
                <li>• Please save this confirmation for your records</li>
                <li>• Our driver will contact you 30 minutes before pickup</li>
                <li>• For any changes or cancellations, please contact us at least 24 hours in advance</li>
                <li>• Keep your booking ID handy: <strong>{bookingId}</strong></li>
                <li>• For support, call +52 ************ <NAME_EMAIL></li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default BookingConfirmation;
